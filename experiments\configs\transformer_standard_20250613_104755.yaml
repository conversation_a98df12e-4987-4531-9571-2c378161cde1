batch_size: 32
data_path: f:/featdata/top
description: 标准Transformer模型
device: cpu
epochs: 50
experiment_name: transformer_standard
fd_set:
- - 1
  - 0
- - 1
  - 1
keep_period_separate: true
learning_rate: 0.001
model_params: {}
model_type: standard_transformer
patience: 20
scheduler: cosine
seed: 42
sel_fd_names:
- NEW_CHANGE_PERCENT
- MACD
- MACD_DIFF
- MACD_DEA
- RSI
- SQUEEZE_NARROW_BARS
- SQUEEZE_ZERO_BARS
- LR_SLOPE_FAST
- LR_SLOPE_MIDD
- LR_SLOPE_SLOW
- LR_SLOPE_FAST_THRESHOLD
- LR_SLOPE_SLOW_THRESHOLD
- STDDEV_FAST
- STDDEV_SLOW
- STDDEV_THRESHOLD
- MOMENTUM_FAST
- MOMENTUM_MIDD
- MOMENTUM_SLOW
- MOMENTUM
- MOMENTUM_THRESHOLD
- SQUEEZE_ZERO_BARS
- SQUEEZE_GAP
- SQUEEZE_GAP_FAST
- SQUEEZE_GAP_SLOW
- SQUEEZE_GAP_THRESHOLD
- SQUEEZE_NARROW_BARS
- BAND_POSITION
- BAND_WIDTH
- BAND_EXPAND
- BAND_GRADIENT
- BAND_GRADIENT_THRESHOLD
- BAND_GAP
- TL_FAST
- TL_SLOW
- TL_THRESHOLD
- TREND_BARS
- TREND_INBARS
- TREND_INPOSR
- TREND_HLR
- TREND_LEVEL
seq_len: 15
tags:
- transformer
- attention
- standard
val_split: 0.2
weight_decay: 0.0001
win: 15
years:
- '2025'
