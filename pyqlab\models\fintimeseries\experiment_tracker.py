#!/usr/bin/env python3
"""
实验跟踪和配置管理系统
"""

import json
import yaml
import pickle
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional
import torch
import numpy as np
from dataclasses import dataclass, asdict
import matplotlib.pyplot as plt
import seaborn as sns


@dataclass
class ExperimentConfig:
    """实验配置类"""
    # 实验基本信息
    experiment_name: str
    description: str
    tags: List[str]

    # 数据配置
    data_path: str
    years: List[str]
    fd_set: List[tuple]
    win: int
    seq_len: int
    keep_period_separate: bool
    sel_fd_names: List[str]  # 选择的因子名称列表

    # 模型配置
    model_type: str
    model_params: Dict[str, Any]

    # 训练配置
    learning_rate: float
    weight_decay: float
    batch_size: int
    epochs: int
    patience: int
    scheduler: str

    # 其他配置
    device: str
    seed: int
    val_split: float
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        # 将fd_set中的元组转换为列表，以便YAML序列化
        if 'fd_set' in data and data['fd_set']:
            data['fd_set'] = [list(item) if isinstance(item, tuple) else item for item in data['fd_set']]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentConfig':
        """从字典创建"""
        # 将fd_set中的列表转换回元组
        if 'fd_set' in data and data['fd_set']:
            data['fd_set'] = [tuple(item) if isinstance(item, list) else item for item in data['fd_set']]
        return cls(**data)
    
    def save(self, path: str):
        """保存配置"""
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(self.to_dict(), f, default_flow_style=False, allow_unicode=True)
    
    @classmethod
    def load(cls, path: str) -> 'ExperimentConfig':
        """加载配置"""
        with open(path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return cls.from_dict(data)


@dataclass
class ExperimentResult:
    """实验结果类"""
    experiment_id: str
    config: ExperimentConfig
    
    # 训练结果
    best_val_loss: float
    final_train_loss: float
    final_val_loss: float
    
    # 评估指标
    final_rmse: float
    final_mae: float
    final_correlation: float
    
    # 训练信息
    total_epochs: int
    training_time: float
    model_params: int
    
    # 历史记录
    train_loss_history: List[float]
    val_loss_history: List[float]
    metrics_history: List[Dict[str, float]]
    
    # 时间戳
    start_time: str
    end_time: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = asdict(self)
        result['config'] = self.config.to_dict()
        return result
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ExperimentResult':
        """从字典创建"""
        config_data = data.pop('config')
        config = ExperimentConfig.from_dict(config_data)
        return cls(config=config, **data)


class ExperimentTracker:
    """实验跟踪器"""
    
    def __init__(self, base_dir: str = "experiments"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        (self.base_dir / "configs").mkdir(exist_ok=True)
        (self.base_dir / "results").mkdir(exist_ok=True)
        (self.base_dir / "models").mkdir(exist_ok=True)
        (self.base_dir / "plots").mkdir(exist_ok=True)
        
        self.current_experiment = None
        self.results_db = self._load_results_db()
    
    def _load_results_db(self) -> List[ExperimentResult]:
        """加载结果数据库"""
        db_path = self.base_dir / "results_db.pkl"
        if db_path.exists():
            with open(db_path, 'rb') as f:
                return pickle.load(f)
        return []
    
    def _save_results_db(self):
        """保存结果数据库"""
        db_path = self.base_dir / "results_db.pkl"
        with open(db_path, 'wb') as f:
            pickle.dump(self.results_db, f)
    
    def create_experiment(self, config: ExperimentConfig) -> str:
        """创建新实验"""
        # 生成实验ID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        experiment_id = f"{config.experiment_name}_{timestamp}"
        
        # 保存配置
        config_path = self.base_dir / "configs" / f"{experiment_id}.yaml"
        config.save(str(config_path))
        
        self.current_experiment = {
            'id': experiment_id,
            'config': config,
            'start_time': datetime.now().isoformat()
        }
        
        print(f"创建实验: {experiment_id}")
        return experiment_id
    
    def log_metrics(self, epoch: int, train_metrics: Dict[str, float], 
                   val_metrics: Dict[str, float]):
        """记录训练指标"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        if 'metrics_history' not in self.current_experiment:
            self.current_experiment['metrics_history'] = []
        
        self.current_experiment['metrics_history'].append({
            'epoch': epoch,
            'train': train_metrics,
            'val': val_metrics
        })
    
    def save_model(self, model: torch.nn.Module, optimizer: torch.optim.Optimizer, 
                   epoch: int, metrics: Dict[str, float]):
        """保存模型"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        experiment_id = self.current_experiment['id']
        model_path = self.base_dir / "models" / f"{experiment_id}_best.pth"
        
        torch.save({
            'experiment_id': experiment_id,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'epoch': epoch,
            'metrics': metrics,
            'config': self.current_experiment['config'].to_dict()
        }, model_path)
        
        print(f"模型已保存: {model_path}")
    
    def finish_experiment(self, final_metrics: Dict[str, float], 
                         model_params: int, training_time: float) -> ExperimentResult:
        """完成实验"""
        if not self.current_experiment:
            raise ValueError("没有活跃的实验")
        
        experiment_id = self.current_experiment['id']
        config = self.current_experiment['config']
        
        # 提取训练历史
        metrics_history = self.current_experiment.get('metrics_history', [])
        train_losses = [m['train']['total'] for m in metrics_history]
        val_losses = [m['val']['total'] for m in metrics_history]
        
        # 创建结果对象
        result = ExperimentResult(
            experiment_id=experiment_id,
            config=config,
            best_val_loss=min(val_losses) if val_losses else 0,
            final_train_loss=train_losses[-1] if train_losses else 0,
            final_val_loss=val_losses[-1] if val_losses else 0,
            final_rmse=final_metrics.get('rmse', 0),
            final_mae=final_metrics.get('mae', 0),
            final_correlation=final_metrics.get('correlation', 0),
            total_epochs=len(train_losses),
            training_time=training_time,
            model_params=model_params,
            train_loss_history=train_losses,
            val_loss_history=val_losses,
            metrics_history=metrics_history,
            start_time=self.current_experiment['start_time'],
            end_time=datetime.now().isoformat()
        )
        
        # 保存结果
        result_path = self.base_dir / "results" / f"{experiment_id}.json"
        with open(result_path, 'w', encoding='utf-8') as f:
            json.dump(result.to_dict(), f, indent=2, ensure_ascii=False)
        
        # 添加到数据库
        self.results_db.append(result)
        self._save_results_db()
        
        # 生成报告
        self._generate_experiment_report(result)
        
        self.current_experiment = None
        print(f"实验完成: {experiment_id}")
        
        return result
    
    def _generate_experiment_report(self, result: ExperimentResult):
        """生成实验报告"""
        experiment_id = result.experiment_id
        
        # 创建训练曲线图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        epochs = range(len(result.train_loss_history))
        
        # 损失曲线
        axes[0, 0].plot(epochs, result.train_loss_history, label='Train Loss')
        axes[0, 0].plot(epochs, result.val_loss_history, label='Val Loss')
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # RMSE曲线
        rmse_history = [m['val'].get('rmse', 0) for m in result.metrics_history]
        if any(rmse_history):
            axes[0, 1].plot(epochs, rmse_history, color='orange')
            axes[0, 1].set_title('RMSE Curve')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('RMSE')
            axes[0, 1].grid(True)
        
        # 相关系数曲线
        corr_history = [m['val'].get('correlation', 0) for m in result.metrics_history]
        if any(corr_history):
            axes[1, 0].plot(epochs, corr_history, color='green')
            axes[1, 0].set_title('Correlation Curve')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Correlation')
            axes[1, 0].grid(True)
        
        # 实验信息
        info_text = f"""
        实验ID: {experiment_id}
        模型: {result.config.model_type}
        最佳验证损失: {result.best_val_loss:.4f}
        最终RMSE: {result.final_rmse:.4f}
        最终相关系数: {result.final_correlation:.4f}
        训练时间: {result.training_time:.1f}s
        模型参数: {result.model_params:,}
        """
        
        axes[1, 1].text(0.1, 0.5, info_text, transform=axes[1, 1].transAxes,
                        fontsize=10, verticalalignment='center',
                        bbox=dict(boxstyle='round', facecolor='lightgray'))
        axes[1, 1].set_xlim(0, 1)
        axes[1, 1].set_ylim(0, 1)
        axes[1, 1].axis('off')
        
        plt.tight_layout()
        plot_path = self.base_dir / "plots" / f"{experiment_id}_training.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"训练曲线已保存: {plot_path}")
    
    def get_experiment_summary(self) -> pd.DataFrame:
        """获取实验汇总"""
        if not self.results_db:
            return pd.DataFrame()
        
        summary_data = []
        for result in self.results_db:
            summary_data.append({
                'Experiment_ID': result.experiment_id,
                'Model_Type': result.config.model_type,
                'Best_Val_Loss': result.best_val_loss,
                'Final_RMSE': result.final_rmse,
                'Final_Correlation': result.final_correlation,
                'Training_Time': result.training_time,
                'Model_Params': result.model_params,
                'Total_Epochs': result.total_epochs,
                'Learning_Rate': result.config.learning_rate,
                'Batch_Size': result.config.batch_size,
                'Start_Time': result.start_time
            })
        
        return pd.DataFrame(summary_data)
    
    def compare_experiments(self, experiment_ids: List[str] = None) -> None:
        """对比实验结果"""
        if experiment_ids is None:
            # 使用所有实验
            experiments = self.results_db
        else:
            experiments = [r for r in self.results_db if r.experiment_id in experiment_ids]
        
        if not experiments:
            print("没有找到实验结果")
            return
        
        # 创建对比图
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 验证损失对比
        for exp in experiments:
            epochs = range(len(exp.val_loss_history))
            axes[0, 0].plot(epochs, exp.val_loss_history, 
                           label=f"{exp.config.model_type}_{exp.experiment_id[-6:]}")
        
        axes[0, 0].set_title('Validation Loss Comparison')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # 2. 最终指标对比
        model_names = [exp.config.model_type for exp in experiments]
        rmse_values = [exp.final_rmse for exp in experiments]
        
        axes[0, 1].bar(range(len(model_names)), rmse_values)
        axes[0, 1].set_title('Final RMSE Comparison')
        axes[0, 1].set_ylabel('RMSE')
        axes[0, 1].set_xticks(range(len(model_names)))
        axes[0, 1].set_xticklabels(model_names, rotation=45)
        
        # 3. 相关系数对比
        corr_values = [exp.final_correlation for exp in experiments]
        axes[1, 0].bar(range(len(model_names)), corr_values)
        axes[1, 0].set_title('Final Correlation Comparison')
        axes[1, 0].set_ylabel('Correlation')
        axes[1, 0].set_xticks(range(len(model_names)))
        axes[1, 0].set_xticklabels(model_names, rotation=45)
        
        # 4. 效率对比 (训练时间 vs 性能)
        training_times = [exp.training_time for exp in experiments]
        axes[1, 1].scatter(training_times, rmse_values, s=100)
        
        for i, exp in enumerate(experiments):
            axes[1, 1].annotate(exp.config.model_type, 
                               (training_times[i], rmse_values[i]),
                               xytext=(5, 5), textcoords='offset points')
        
        axes[1, 1].set_title('Training Time vs Performance')
        axes[1, 1].set_xlabel('Training Time (s)')
        axes[1, 1].set_ylabel('RMSE')
        axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # 保存对比图
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        comparison_path = self.base_dir / "plots" / f"comparison_{timestamp}.png"
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        print(f"对比图已保存: {comparison_path}")
    
    def load_experiment_result(self, experiment_id: str) -> Optional[ExperimentResult]:
        """加载特定实验结果"""
        for result in self.results_db:
            if result.experiment_id == experiment_id:
                return result
        return None
    
    def export_results(self, format: str = 'csv') -> str:
        """导出结果"""
        summary_df = self.get_experiment_summary()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format == 'csv':
            export_path = self.base_dir / f"experiment_summary_{timestamp}.csv"
            summary_df.to_csv(export_path, index=False)
        elif format == 'excel':
            export_path = self.base_dir / f"experiment_summary_{timestamp}.xlsx"
            summary_df.to_excel(export_path, index=False)
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        print(f"结果已导出: {export_path}")
        return str(export_path)


# 预定义实验配置模板
def get_experiment_templates() -> Dict[str, ExperimentConfig]:
    """获取实验配置模板"""
    from pyqlab.const import SEL_FACTOR_NAMES
    base_config = {
        'data_path': "f:/featdata/top",
        'years': ["2025"],
        'fd_set': [(1, 0), (1, 1)],
        'win': 15,
        'seq_len': 15,
        'sel_fd_names': SEL_FACTOR_NAMES,
        'keep_period_separate': True,
        'device': 'cuda' if torch.cuda.is_available() else 'cpu',
        'seed': 42,
        'val_split': 0.2
    }
    
    templates = {
        'cnn_baseline': ExperimentConfig(
            experiment_name='cnn_baseline',
            description='轻量级CNN基线模型',
            tags=['baseline', 'cnn', 'fast'],
            model_type='lightweight_cnn',
            model_params={},
            learning_rate=1e-3,
            weight_decay=1e-4,
            batch_size=32,
            epochs=100,
            patience=20,
            scheduler='cosine',
            **base_config
        ),
        
        'transformer_standard': ExperimentConfig(
            experiment_name='transformer_standard',
            description='标准Transformer模型',
            tags=['transformer', 'attention', 'standard'],
            model_type='standard_transformer',
            model_params={},
            learning_rate=3e-4,
            weight_decay=1e-4,
            batch_size=32,
            epochs=100,
            patience=20,
            scheduler='cosine',
            **base_config
        ),
        
        'graph_advanced': ExperimentConfig(
            experiment_name='graph_advanced',
            description='高级图神经网络模型',
            tags=['graph', 'gnn', 'advanced'],
            model_type='advanced_graph',
            model_params={},
            learning_rate=1e-3,
            weight_decay=1e-4,
            batch_size=32,
            epochs=100,
            patience=20,
            scheduler='cosine',
            **base_config
        )
    }
    
    return templates


if __name__ == "__main__":
    # 示例使用
    tracker = ExperimentTracker("experiments")
    
    # 获取模板配置
    templates = get_experiment_templates()
    
    # 创建实验
    config = templates['cnn_baseline']
    experiment_id = tracker.create_experiment(config)
    
    print(f"实验配置已保存，ID: {experiment_id}")
    
    # 查看实验汇总
    summary = tracker.get_experiment_summary()
    print("\n实验汇总:")
    print(summary)
