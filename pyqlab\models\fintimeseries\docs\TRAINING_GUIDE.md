# 新模型架构训练指南

## 🚀 快速开始

### 1. 基本训练

```powershell
# 训练单个模型
python -m pyqlab.models.fintimeseries.train_with_tracking --mode single --config cnn_baseline

# 训练并指定参数
python -m pyqlab.models.fintimeseries.train_with_tracking --mode single --config transformer_standard --epochs 50 --lr 1e-3 --batch_size 32
```

### 2. 模型对比

```powershell
# 对比多个模型
python -m pyqlab.models.fintimeseries.train_with_tracking --mode compare --configs cnn_baseline transformer_standard graph_advanced

# 快速对比（减少训练轮数）
python -m pyqlab.models.fintimeseries.train_with_tracking --mode compare --configs cnn_baseline transformer_standard --epochs 30
```

### 3. 超参数搜索

```powershell
# 自动超参数搜索
python -m pyqlab.models.fintimeseries.train_with_tracking --mode search --config cnn_baseline
```

## 📋 可用模型配置

### 预设配置列表

| 配置名称 | 模型类型 | 特点 | 适用场景 |
|---------|---------|------|----------|
| `cnn_baseline` | CNN | 轻量级、快速 | 基线模型、快速验证 |
| `transformer_standard` | Transformer | 标准注意力机制 | 中等规模数据 |
| `graph_advanced` | Graph | 图神经网络 | 复杂关系建模 |

### 自定义配置

```python
from pyqlab.models.fintimeseries.experiment_tracker import ExperimentConfig

# 创建自定义配置
custom_config = ExperimentConfig(
    experiment_name='my_experiment',
    description='自定义实验',
    tags=['custom', 'test'],
    
    # 数据配置
    data_path="f:/featdata/tmp",
    years=["2025"],
    fd_set=[(1, 0), (1, 1), (2, 0)],
    win=20,
    seq_len=20,
    keep_period_separate=True,
    
    # 模型配置
    model_type='lightweight_cnn',
    model_params={},
    
    # 训练配置
    learning_rate=1e-3,
    weight_decay=1e-4,
    batch_size=32,
    epochs=100,
    patience=20,
    scheduler='cosine',
    
    # 其他配置
    device='cuda',
    seed=42,
    val_split=0.2
)
```

## 🔧 高级功能

### 1. 实验跟踪

所有训练过程都会自动跟踪：

- **配置管理**: 自动保存实验配置
- **指标记录**: 实时记录训练和验证指标
- **模型保存**: 自动保存最佳模型
- **可视化**: 生成训练曲线和对比图
- **结果导出**: 支持CSV/Excel格式导出

### 2. 实验管理

```python
from pyqlab.models.fintimeseries.experiment_tracker import ExperimentTracker

# 创建跟踪器
tracker = ExperimentTracker("experiments")

# 查看所有实验
summary = tracker.get_experiment_summary()
print(summary)

# 对比特定实验
tracker.compare_experiments(['experiment_id_1', 'experiment_id_2'])

# 导出结果
tracker.export_results('csv')
```

### 3. 模型工厂

```python
from pyqlab.models.fintimeseries.model_factory import ModelFactory, ModelConfig

# 创建模型配置
config = ModelConfig(
    model_type='transformer',
    period_configs=[
        {'input_dim': 51},  # fd_1_0
        {'input_dim': 51},  # fd_1_1
        {'input_dim': 8},   # fd_2_0
    ],
    task_type='regression',
    probabilistic=True
)

# 创建模型
model = ModelFactory.create_model(config)
```

## 📊 数据配置

### 数据路径设置

确保数据路径正确设置：

```python
data_config = {
    "data_path": "f:/featdata/tmp",  # 修改为您的数据路径
    "years": ["2025"],               # 数据年份
    "fd_set": {(1,0), (1,1), (2,0)}, # 多周期配置
    "win": 20,                       # 窗口大小
    "seq_len": 20,                   # 序列长度
    "keep_period_separate": True     # 保持周期分离
}
```

### 多周期数据结构

支持的周期配置：
- `(1,0)`: fd_1_0 - 短期因子 (51维)
- `(1,1)`: fd_1_1 - 中期因子 (51维)  
- `(2,0)`: fd_2_0 - 长期因子 (8维)

## 🎯 训练策略

### 1. 基线建立

```powershell
# 首先训练轻量级CNN作为基线
python -m pyqlab.models.fintimeseries.train_with_tracking --mode single --config cnn_baseline --epochs 100
```

### 2. 模型选择

```powershell
# 对比不同架构
python -m pyqlab.models.fintimeseries.train_with_tracking --mode compare --configs cnn_baseline transformer_standard graph_advanced --epochs 50
```

### 3. 超参数优化

```powershell
# 对最佳模型进行超参数搜索
python -m pyqlab.models.fintimeseries.train_with_tracking --mode search --config transformer_standard
```

### 4. 最终训练

```powershell
# 使用最佳参数进行完整训练
python -m pyqlab.models.fintimeseries.train_with_tracking --mode single --config transformer_standard --epochs 200 --lr 3e-4 --batch_size 32
```

## 📈 性能监控

### 训练指标

- **损失函数**: MSE Loss (回归), CrossEntropy (分类)
- **评估指标**: RMSE, MAE, Correlation
- **训练效率**: 训练时间, 参数量, 内存使用

### 早停机制

- **Patience**: 默认20个epoch无改善则停止
- **最佳模型**: 自动保存验证损失最低的模型
- **学习率调度**: 支持Cosine、Step、ReduceLROnPlateau

## 🔍 结果分析

### 实验目录结构

```
experiments/
├── configs/           # 实验配置文件
├── results/           # 实验结果JSON
├── models/            # 保存的模型文件
├── plots/             # 训练曲线和对比图
├── results_db.pkl     # 结果数据库
└── experiment_summary_*.csv  # 导出的汇总表
```

### 可视化输出

1. **训练曲线**: 损失、RMSE、相关系数变化
2. **模型对比**: 多模型性能对比图
3. **效率分析**: 训练时间vs性能散点图

## ⚠️ 注意事项

### 1. 环境要求

- Python 3.8+
- PyTorch 1.9+
- CUDA (推荐)
- 足够的内存 (建议16GB+)

### 2. 数据要求

- 确保数据文件存在且格式正确
- 检查特征维度匹配
- 验证标签数据完整性

### 3. 训练建议

- **小数据集**: 使用CNN模型，减少epoch
- **中等数据集**: 使用Transformer，标准配置
- **大数据集**: 使用MoE或集成模型
- **GPU内存不足**: 减少batch_size或使用梯度累积

### 4. 常见问题

**Q: 训练过程中内存不足？**
A: 减少batch_size，或使用`torch.cuda.empty_cache()`

**Q: 模型不收敛？**
A: 检查学习率设置，尝试更小的学习率或不同的优化器

**Q: 验证损失不下降？**
A: 可能过拟合，增加dropout或减少模型复杂度

**Q: 数据加载失败？**
A: 检查数据路径和文件格式，确保FTSDataset配置正确

## 📝 最佳实践

### 1. 实验流程

1. **数据验证**: 先用小数据集验证流程
2. **基线建立**: 训练简单模型作为基线
3. **模型对比**: 比较不同架构性能
4. **超参数调优**: 优化最佳模型
5. **最终训练**: 完整训练并验证

### 2. 配置管理

- 为每个实验设置清晰的名称和描述
- 使用标签分类不同类型的实验
- 定期备份实验结果和配置

### 3. 性能优化

- 使用混合精度训练 (AMP)
- 启用数据并行 (DataParallel)
- 优化数据加载 (num_workers)
- 使用梯度累积处理大batch

### 4. 结果分析

- 关注验证集性能而非训练集
- 分析训练曲线识别过拟合
- 对比不同模型的效率和精度
- 保存和分享最佳配置

## 🎉 总结

这套训练框架提供了：

✅ **完整的模型架构**: 4种先进的深度学习模型  
✅ **自动化训练**: 端到端的训练流程  
✅ **实验跟踪**: 完整的实验管理系统  
✅ **性能对比**: 多模型对比和分析  
✅ **超参数优化**: 自动化参数搜索  
✅ **可视化分析**: 丰富的图表和报告  

开始您的模型训练之旅吧！🚀
