@echo off
echo ========================================
echo TimeSeriesModel2drV2 Backtest Script
echo ========================================
e:
cd e:\lab\RoboQuant\pylab\pyqlab\models\fintimeseries

REM Set default parameters
@REM FUT_CV2DR_F_15HF_1600_060411_0.015_ls.onnx
@REM FUT_CV2DR2_F_15HF_1600_060409_0.014_ls.onnx
set MODEL_PATH=E:\lab\RoboQuant\pylab\model\FUT_CV2DR_F_15HF_1600_060411_0.015_ls.onnx
set DATA_PATH=f:/featdata/top
set OUTPUT_DIR=./backtest_results
set INITIAL_CAPITAL=10000
set LEVERAGE=1.0
set COMMISSION=0.001
set THRESHOLD=0.012
set MAX_DATA_LEN=10000

REM Check if model path parameter is provided
if "%1"=="" (
    echo Using default model path: %MODEL_PATH%
) else (
    set MODEL_PATH=%1
    echo Using specified model path: %MODEL_PATH%
)

REM Check if model file exists
if not exist "%MODEL_PATH%" (
    echo ERROR: Model file does not exist: %MODEL_PATH%
    echo Please ensure the model file path is correct
    pause
    exit /b 1
)

echo.
echo Backtest Parameters:
echo - Model Path: %MODEL_PATH%
echo - Data Path: %DATA_PATH%
echo - Output Directory: %OUTPUT_DIR%
echo - Initial Capital: %INITIAL_CAPITAL%
echo - Leverage: %LEVERAGE%
echo - Commission Rate: %COMMISSION%
echo - Signal Threshold: %THRESHOLD%
echo.

REM Create output directory
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Run backtest script
echo Starting backtest...
python backtest_time_series_model2dr_v2.py ^
    --model_path "%MODEL_PATH%" ^
    --model_type onnx ^
    --data_path "%DATA_PATH%" ^
    --ds_files "[\"top.2025\"]" ^
    --fut_codes "[\"RB\"]" ^
    --seq_len 15 ^
    --initial_capital %INITIAL_CAPITAL% ^
    --commission %COMMISSION% ^
    --threshold %THRESHOLD% ^
    --leverage %LEVERAGE% ^
    --output_dir "%OUTPUT_DIR%" ^
    --save_chart ^
    --print_interval 200 ^
    --max_data_len %MAX_DATA_LEN%

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo Backtest completed successfully!
    echo Results saved to: %OUTPUT_DIR%
    echo ========================================
) else (
    echo.
    echo ========================================
    echo Backtest failed with error code: %ERRORLEVEL%
    echo ========================================
)

