#!/usr/bin/env python3
"""
带实验跟踪的完整训练脚本
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import numpy as np
import time
import argparse
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from .train_new_models import MultiPeriodDataset, ModelTrainer, create_data_loaders
from .model_factory import ModelFactory, get_preset_configs
from .experiment_tracker import ExperimentTracker, ExperimentConfig, get_experiment_templates


class TrackedModelTrainer(ModelTrainer):
    """带跟踪功能的模型训练器"""
    
    def __init__(self, model: nn.Module, config: dict, tracker: ExperimentTracker, device: str = 'cuda'):
        super().__init__(model, config, device)
        self.tracker = tracker
        
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              epochs: int = 100, save_best: bool = True) -> dict:
        """带跟踪的训练流程"""
        best_val_loss = float('inf')
        patience_counter = 0
        patience = self.config.get('patience', 20)
        
        print(f"开始训练，共 {epochs} 个epoch")
        print(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        start_time = time.time()
        
        for epoch in range(epochs):
            epoch_start = time.time()
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.evaluate(val_loader)
            
            # 更新学习率
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_metrics['total'])
            else:
                self.scheduler.step()
            
            # 记录到跟踪器
            self.tracker.log_metrics(epoch, train_metrics, val_metrics)
            
            # 记录历史
            self.training_history['train_loss'].append(train_metrics['total'])
            self.training_history['val_loss'].append(val_metrics['total'])
            self.training_history['train_metrics'].append(train_metrics)
            self.training_history['val_metrics'].append(val_metrics)
            
            epoch_time = time.time() - epoch_start
            
            # 打印进度
            if epoch % 10 == 0 or epoch == epochs - 1:
                print(f"Epoch {epoch:3d}/{epochs} | "
                      f"Train Loss: {train_metrics['total']:.4f} | "
                      f"Val Loss: {val_metrics['total']:.4f} | "
                      f"Val RMSE: {val_metrics.get('rmse', 0):.4f} | "
                      f"Val Corr: {val_metrics.get('correlation', 0):.4f} | "
                      f"LR: {self.optimizer.param_groups[0]['lr']:.2e} | "
                      f"Time: {epoch_time:.1f}s")
            
            # 保存最佳模型
            if val_metrics['total'] < best_val_loss:
                best_val_loss = val_metrics['total']
                patience_counter = 0
                
                if save_best:
                    self.tracker.save_model(self.model, self.optimizer, epoch, val_metrics)
            else:
                patience_counter += 1
                
                if patience_counter >= patience:
                    print(f"早停于第 {epoch} epoch，最佳验证损失: {best_val_loss:.4f}")
                    break
        
        total_time = time.time() - start_time
        
        # 完成实验跟踪
        final_metrics = self.training_history['val_metrics'][-1]
        model_params = sum(p.numel() for p in self.model.parameters())
        
        result = self.tracker.finish_experiment(final_metrics, model_params, total_time)
        
        print(f"训练完成！总时间: {total_time:.1f}秒")
        print(f"实验结果已保存: {result.experiment_id}")
        
        return self.training_history


def run_single_experiment(config_name: str, custom_params: dict = None):
    """运行单个实验"""
    # 创建实验跟踪器
    tracker = ExperimentTracker("experiments")
    
    # 获取实验配置
    templates = get_experiment_templates()
    if config_name not in templates:
        raise ValueError(f"未找到配置模板: {config_name}")
    
    config = templates[config_name]
    
    # 应用自定义参数
    if custom_params:
        for key, value in custom_params.items():
            if hasattr(config, key):
                setattr(config, key, value)
    
    # 设置随机种子
    torch.manual_seed(config.seed)
    np.random.seed(config.seed)
    
    # 创建实验
    experiment_id = tracker.create_experiment(config)
    
    try:
        # 准备数据配置
        data_config = {
            "data_path": config.data_path,
            "years": config.years,
            "fd_set": set(config.fd_set),
            "win": config.win,
            "step": 1,
            "seq_len": config.seq_len,
            "verbose": False,
            "is_normal": True,
            "keep_period_separate": config.keep_period_separate
        }
        
        # 获取模型配置
        preset_configs = get_preset_configs()
        model_config = preset_configs[config.model_type]
        
        # 创建数据加载器
        print("创建数据加载器...")
        train_loader, val_loader = create_data_loaders(
            data_config, model_config, 
            batch_size=config.batch_size, 
            val_split=config.val_split
        )
        
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        # 创建模型
        print("创建模型...")
        model = ModelFactory.create_model(model_config)
        
        # 训练配置
        train_config = {
            'learning_rate': config.learning_rate,
            'weight_decay': config.weight_decay,
            'epochs': config.epochs,
            'patience': config.patience,
            'scheduler': config.scheduler,
            'task_type': model_config.task_type
        }
        
        # 创建训练器
        trainer = TrackedModelTrainer(model, train_config, tracker, config.device)
        
        # 开始训练
        history = trainer.train(train_loader, val_loader, epochs=config.epochs)
        
        return experiment_id, history
        
    except Exception as e:
        print(f"实验失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def run_comparison_experiments(config_names: list, custom_params: dict = None):
    """运行对比实验"""
    print(f"开始对比实验，模型: {config_names}")
    
    experiment_ids = []
    
    for config_name in config_names:
        print(f"\n{'='*60}")
        print(f"运行实验: {config_name}")
        print(f"{'='*60}")
        
        experiment_id, history = run_single_experiment(config_name, custom_params)
        
        if experiment_id:
            experiment_ids.append(experiment_id)
            print(f"实验 {config_name} 完成: {experiment_id}")
        else:
            print(f"实验 {config_name} 失败")
    
    # 生成对比报告
    if experiment_ids:
        tracker = ExperimentTracker("experiments")
        
        print(f"\n{'='*60}")
        print("生成对比报告")
        print(f"{'='*60}")
        
        # 显示汇总表
        summary = tracker.get_experiment_summary()
        recent_experiments = summary[summary['Experiment_ID'].isin(experiment_ids)]
        print("\n实验结果汇总:")
        print(recent_experiments.to_string(index=False))
        
        # 生成对比图
        tracker.compare_experiments(experiment_ids)
        
        # 导出结果
        export_path = tracker.export_results('csv')
        print(f"结果已导出: {export_path}")
    
    return experiment_ids


def run_hyperparameter_search(base_config_name: str, search_space: dict, max_trials: int = 20):
    """运行超参数搜索"""
    print(f"开始超参数搜索，基础配置: {base_config_name}")
    print(f"搜索空间: {search_space}")
    print(f"最大试验次数: {max_trials}")
    
    # 获取基础配置
    templates = get_experiment_templates()
    base_config = templates[base_config_name]
    
    best_score = float('inf')
    best_params = {}
    experiment_ids = []
    
    # 简单网格搜索
    import itertools
    
    # 生成参数组合
    param_names = list(search_space.keys())
    param_values = list(search_space.values())
    param_combinations = list(itertools.product(*param_values))
    
    # 限制试验次数
    if len(param_combinations) > max_trials:
        import random
        param_combinations = random.sample(param_combinations, max_trials)
    
    for i, param_combo in enumerate(param_combinations):
        print(f"\n试验 {i+1}/{len(param_combinations)}")
        
        # 创建参数字典
        trial_params = dict(zip(param_names, param_combo))
        print(f"参数: {trial_params}")
        
        # 修改实验名称
        trial_params['experiment_name'] = f"{base_config_name}_trial_{i+1}"
        
        # 运行实验
        experiment_id, history = run_single_experiment(base_config_name, trial_params)
        
        if experiment_id and history:
            experiment_ids.append(experiment_id)
            
            # 评估结果
            best_val_loss = min(history['val_loss'])
            print(f"验证损失: {best_val_loss:.4f}")
            
            if best_val_loss < best_score:
                best_score = best_val_loss
                best_params = trial_params
                print("*** 新的最佳参数! ***")
    
    print(f"\n超参数搜索完成!")
    print(f"最佳参数: {best_params}")
    print(f"最佳验证损失: {best_score:.4f}")
    
    # 生成搜索报告
    if experiment_ids:
        tracker = ExperimentTracker("experiments")
        summary = tracker.get_experiment_summary()
        search_results = summary[summary['Experiment_ID'].isin(experiment_ids)]
        
        print("\n搜索结果汇总:")
        print(search_results.to_string(index=False))
        
        # 保存搜索结果
        search_results.to_csv(f"hyperparameter_search_{base_config_name}.csv", index=False)
    
    return best_params, experiment_ids


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='带跟踪的模型训练')
    parser.add_argument('--mode', type=str, default='single',
                       choices=['single', 'compare', 'search'],
                       help='运行模式')
    parser.add_argument('--config', type=str, default='cnn_baseline',
                       help='配置名称')
    parser.add_argument('--configs', type=str, nargs='+',
                       default=['cnn_baseline', 'transformer_standard'],
                       help='对比模式下的配置列表')
    parser.add_argument('--epochs', type=int, default=None,
                       help='训练轮数')
    parser.add_argument('--lr', type=float, default=None,
                       help='学习率')
    parser.add_argument('--batch_size', type=int, default=None,
                       help='批次大小')
    
    args = parser.parse_args()
    
    # 准备自定义参数
    custom_params = {}
    if args.epochs is not None:
        custom_params['epochs'] = args.epochs
    if args.lr is not None:
        custom_params['learning_rate'] = args.lr
    if args.batch_size is not None:
        custom_params['batch_size'] = args.batch_size
    
    if args.mode == 'single':
        print(f"运行单个实验: {args.config}")
        experiment_id, history = run_single_experiment(args.config, custom_params)
        
        if experiment_id:
            print(f"实验完成: {experiment_id}")
        
    elif args.mode == 'compare':
        print(f"运行对比实验: {args.configs}")
        experiment_ids = run_comparison_experiments(args.configs, custom_params)
        
        print(f"对比实验完成，共 {len(experiment_ids)} 个实验")
        
    elif args.mode == 'search':
        print(f"运行超参数搜索: {args.config}")
        
        # 定义搜索空间
        search_space = {
            'learning_rate': [1e-4, 3e-4, 1e-3],
            'weight_decay': [1e-5, 1e-4, 1e-3],
            'batch_size': [16, 32, 64]
        }
        
        best_params, experiment_ids = run_hyperparameter_search(
            args.config, search_space, max_trials=15
        )
        
        print(f"超参数搜索完成，最佳参数: {best_params}")


if __name__ == "__main__":
    main()
