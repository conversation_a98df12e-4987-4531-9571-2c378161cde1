#!/usr/bin/env python3
"""
新模型架构训练脚本
专门针对多周期时序因子特征向量设计的训练框架
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import time
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 导入数据处理模块
from pyqlab.data.dataset.dataset_fts import FTSDataset
from pyqlab.data.dataset.handler import DataHandler

# 导入新模型
from .model_factory import ModelFactory, ModelConfig, get_preset_configs
from .multi_scale_transformer import MultiScaleTemporalTransformer
from .hierarchical_temporal_cnn import HierarchicalTemporalCNN
from .temporal_graph_network import TemporalGraphNetwork
from .mixture_of_experts import MixtureOfExpertsModel


class MultiPeriodDataset(torch.utils.data.Dataset):
    """
    多周期数据集适配器
    将FTSDataset的输出转换为新模型需要的格式
    """
    
    def __init__(self, fts_dataset: FTSDataset, period_configs: List[Dict[str, int]]):
        self.fts_dataset = fts_dataset
        self.period_configs = period_configs
        self.num_periods = len(period_configs)
        
        # 计算每个周期的特征起始位置
        self.period_starts = []
        start_idx = 0
        for config in period_configs:
            self.period_starts.append(start_idx)
            start_idx += config['input_dim']
        
    def __len__(self):
        return len(self.fts_dataset)
    
    def __getitem__(self, idx):
        # 获取原始数据
        if self.fts_dataset.model_type == 0:
            code, x, y = self.fts_dataset[idx]
        else:
            code, x_data, x_mark, y_data, y_mark = self.fts_dataset[idx]
            x = x_data
            y = y_data
        
        # 检查数据维度
        if len(x.shape) == 4:
            # 周期分离模式: (周期数, 窗口大小, 特征数)
            period_data = []
            for p in range(min(self.num_periods, x.shape[0])):
                period_tensor = x[p]  # (窗口大小, 特征数)
                # 截取或填充到指定维度
                target_dim = self.period_configs[p]['input_dim']
                if period_tensor.shape[-1] >= target_dim:
                    period_tensor = period_tensor[:, :target_dim]
                else:
                    # 零填充
                    padding = torch.zeros(period_tensor.shape[0], target_dim - period_tensor.shape[-1])
                    period_tensor = torch.cat([period_tensor, padding], dim=-1)
                period_data.append(period_tensor)
            
            # 如果周期数不足，用零填充
            while len(period_data) < self.num_periods:
                seq_len = period_data[0].shape[0] if period_data else 20
                target_dim = self.period_configs[len(period_data)]['input_dim']
                period_data.append(torch.zeros(seq_len, target_dim))
                
        else:
            # 合并模式: (窗口大小, 总特征数) -> 分割为多个周期
            period_data = []
            for p in range(self.num_periods):
                start_idx = self.period_starts[p]
                end_idx = start_idx + self.period_configs[p]['input_dim']
                
                if end_idx <= x.shape[-1]:
                    period_tensor = x[:, start_idx:end_idx]
                else:
                    # 如果特征不足，用零填充
                    available_features = max(0, x.shape[-1] - start_idx)
                    if available_features > 0:
                        period_tensor = x[:, start_idx:start_idx + available_features]
                        padding = torch.zeros(x.shape[0], self.period_configs[p]['input_dim'] - available_features)
                        period_tensor = torch.cat([period_tensor, padding], dim=-1)
                    else:
                        period_tensor = torch.zeros(x.shape[0], self.period_configs[p]['input_dim'])
                
                period_data.append(period_tensor)
        
        # 处理代码数据
        if len(code.shape) == 1:
            # 扩展为序列
            seq_len = period_data[0].shape[0]
            code_seq = code[-1].unsqueeze(0).repeat(seq_len)
        else:
            code_seq = code
        
        return period_data, code_seq, y


class ModelTrainer:
    """模型训练器"""
    
    def __init__(self, model: nn.Module, config: Dict[str, Any], device: str = 'cuda'):
        self.model = model.to(device)
        self.config = config
        self.device = device
        self.training_history = {
            'train_loss': [],
            'val_loss': [],
            'train_metrics': [],
            'val_metrics': []
        }
        
        # 设置损失函数
        self.criterion = self._setup_criterion()
        
        # 设置优化器
        self.optimizer = self._setup_optimizer()
        
        # 设置学习率调度器
        self.scheduler = self._setup_scheduler()
        
    def _setup_criterion(self):
        """设置损失函数"""
        task_type = self.config.get('task_type', 'regression')
        
        if task_type == 'regression':
            return nn.MSELoss()
        elif task_type == 'classification':
            return nn.CrossEntropyLoss()
        elif task_type == 'multi_task':
            return {
                'main': nn.MSELoss(),
                'aux': nn.CrossEntropyLoss()
            }
        else:
            return nn.MSELoss()
    
    def _setup_optimizer(self):
        """设置优化器"""
        lr = self.config.get('learning_rate', 1e-3)
        weight_decay = self.config.get('weight_decay', 1e-4)
        
        return optim.AdamW(
            self.model.parameters(),
            lr=lr,
            weight_decay=weight_decay,
            betas=(0.9, 0.999)
        )
    
    def _setup_scheduler(self):
        """设置学习率调度器"""
        scheduler_type = self.config.get('scheduler', 'cosine')
        epochs = self.config.get('epochs', 100)
        
        if scheduler_type == 'cosine':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=epochs, eta_min=1e-6
            )
        elif scheduler_type == 'step':
            return optim.lr_scheduler.StepLR(
                self.optimizer, step_size=epochs//3, gamma=0.5
            )
        else:
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', patience=10, factor=0.5
            )
    
    def compute_loss(self, outputs: Dict[str, torch.Tensor], targets: torch.Tensor) -> Dict[str, torch.Tensor]:
        """计算损失"""
        losses = {}
        total_loss = 0.0
        
        # 主任务损失
        if 'prediction' in outputs:
            main_loss = self.criterion(outputs['prediction'], targets)
        elif 'main_prediction' in outputs:
            main_loss = self.criterion['main'](outputs['main_prediction'], targets)
        elif 'mean' in outputs:
            # 概率预测损失
            mean = outputs['mean']
            var = outputs['var']
            main_loss = 0.5 * (torch.log(var) + (targets - mean) ** 2 / var).mean()
        elif 'logits' in outputs:
            # 分类任务
            main_loss = self.criterion(outputs['logits'], targets.long())
        else:
            main_loss = torch.tensor(0.0, device=self.device)
        
        losses['main'] = main_loss
        total_loss += main_loss
        
        # 辅助任务损失
        if 'aux_logits' in outputs:
            # 将回归目标转换为分类标签
            aux_targets = torch.sign(targets).long() + 1  # -1,0,1 -> 0,1,2
            aux_targets = torch.clamp(aux_targets, 0, 2)
            aux_loss = self.criterion['aux'](outputs['aux_logits'], aux_targets)
            losses['aux'] = aux_loss
            total_loss += 0.3 * aux_loss  # 辅助任务权重
        
        # 负载平衡损失 (MoE)
        if 'load_balance_loss' in outputs:
            load_balance_loss = outputs['load_balance_loss']
            losses['load_balance'] = load_balance_loss
            total_loss += load_balance_loss
        
        losses['total'] = total_loss
        return losses
    
    def train_epoch(self, dataloader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        epoch_losses = {'total': 0.0, 'main': 0.0, 'aux': 0.0, 'load_balance': 0.0}
        num_batches = 0
        
        for batch_idx, (period_data, code_data, targets) in enumerate(dataloader):
            # 移动数据到设备
            period_data = [data.to(self.device) for data in period_data]
            code_data = code_data.to(self.device)
            targets = targets.to(self.device).float()
            
            # 前向传播
            self.optimizer.zero_grad()
            outputs = self.model(period_data, code_data)
            
            # 计算损失
            losses = self.compute_loss(outputs, targets)
            
            # 反向传播
            losses['total'].backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # 更新参数
            self.optimizer.step()
            
            # 累计损失
            for key, value in losses.items():
                if isinstance(value, torch.Tensor):
                    epoch_losses[key] += value.item()
                else:
                    epoch_losses[key] += value
            
            num_batches += 1
        
        # 计算平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def evaluate(self, dataloader: DataLoader) -> Dict[str, float]:
        """评估模型"""
        self.model.eval()
        epoch_losses = {'total': 0.0, 'main': 0.0, 'aux': 0.0, 'load_balance': 0.0}
        predictions = []
        targets_list = []
        num_batches = 0
        
        with torch.no_grad():
            for period_data, code_data, targets in dataloader:
                period_data = [data.to(self.device) for data in period_data]
                code_data = code_data.to(self.device)
                targets = targets.to(self.device).float()
                
                outputs = self.model(period_data, code_data)
                losses = self.compute_loss(outputs, targets)
                
                # 累计损失
                for key, value in losses.items():
                    if isinstance(value, torch.Tensor):
                        epoch_losses[key] += value.item()
                    else:
                        epoch_losses[key] += value
                
                # 收集预测结果
                if 'prediction' in outputs:
                    predictions.append(outputs['prediction'].cpu())
                elif 'main_prediction' in outputs:
                    predictions.append(outputs['main_prediction'].cpu())
                elif 'mean' in outputs:
                    predictions.append(outputs['mean'].cpu())
                
                targets_list.append(targets.cpu())
                num_batches += 1
        
        # 计算平均损失
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        # 计算评估指标
        if predictions:
            predictions = torch.cat(predictions, dim=0)
            targets_tensor = torch.cat(targets_list, dim=0)
            
            mse = nn.MSELoss()(predictions, targets_tensor).item()
            mae = nn.L1Loss()(predictions, targets_tensor).item()
            rmse = np.sqrt(mse)
            
            # 计算相关系数
            pred_np = predictions.numpy().flatten()
            target_np = targets_tensor.numpy().flatten()
            correlation = np.corrcoef(pred_np, target_np)[0, 1]
            
            epoch_losses.update({
                'mse': mse,
                'mae': mae,
                'rmse': rmse,
                'correlation': correlation
            })
        
        return epoch_losses
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              epochs: int = 100, save_path: Optional[str] = None) -> Dict[str, List]:
        """完整训练流程"""
        best_val_loss = float('inf')
        patience_counter = 0
        patience = self.config.get('patience', 20)
        
        print(f"开始训练，共 {epochs} 个epoch")
        print(f"模型参数量: {sum(p.numel() for p in self.model.parameters()):,}")
        
        for epoch in range(epochs):
            start_time = time.time()
            
            # 训练
            train_metrics = self.train_epoch(train_loader)
            
            # 验证
            val_metrics = self.evaluate(val_loader)
            
            # 更新学习率
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_metrics['total'])
            else:
                self.scheduler.step()
            
            # 记录历史
            self.training_history['train_loss'].append(train_metrics['total'])
            self.training_history['val_loss'].append(val_metrics['total'])
            self.training_history['train_metrics'].append(train_metrics)
            self.training_history['val_metrics'].append(val_metrics)
            
            epoch_time = time.time() - start_time
            
            # 打印进度
            if epoch % 10 == 0 or epoch == epochs - 1:
                print(f"Epoch {epoch:3d}/{epochs} | "
                      f"Train Loss: {train_metrics['total']:.4f} | "
                      f"Val Loss: {val_metrics['total']:.4f} | "
                      f"Val RMSE: {val_metrics.get('rmse', 0):.4f} | "
                      f"Val Corr: {val_metrics.get('correlation', 0):.4f} | "
                      f"Time: {epoch_time:.1f}s")
            
            # 早停检查
            if val_metrics['total'] < best_val_loss:
                best_val_loss = val_metrics['total']
                patience_counter = 0
                
                # 保存最佳模型
                if save_path:
                    torch.save({
                        'model_state_dict': self.model.state_dict(),
                        'optimizer_state_dict': self.optimizer.state_dict(),
                        'epoch': epoch,
                        'val_loss': best_val_loss,
                        'config': self.config
                    }, save_path)
            else:
                patience_counter += 1
                
                if patience_counter >= patience:
                    print(f"早停于第 {epoch} epoch，最佳验证损失: {best_val_loss:.4f}")
                    break
        
        return self.training_history
    
    def plot_training_history(self, save_path: Optional[str] = None):
        """绘制训练历史"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 损失曲线
        epochs = range(len(self.training_history['train_loss']))
        axes[0, 0].plot(epochs, self.training_history['train_loss'], label='Train Loss')
        axes[0, 0].plot(epochs, self.training_history['val_loss'], label='Val Loss')
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)
        
        # RMSE曲线
        val_rmse = [m.get('rmse', 0) for m in self.training_history['val_metrics']]
        if any(val_rmse):
            axes[0, 1].plot(epochs, val_rmse, label='Val RMSE', color='orange')
            axes[0, 1].set_title('RMSE Curve')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('RMSE')
            axes[0, 1].legend()
            axes[0, 1].grid(True)
        
        # 相关系数曲线
        val_corr = [m.get('correlation', 0) for m in self.training_history['val_metrics']]
        if any(val_corr):
            axes[1, 0].plot(epochs, val_corr, label='Val Correlation', color='green')
            axes[1, 0].set_title('Correlation Curve')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Correlation')
            axes[1, 0].legend()
            axes[1, 0].grid(True)
        
        # 学习率曲线
        if hasattr(self.scheduler, 'get_last_lr'):
            lr_history = [self.optimizer.param_groups[0]['lr']]  # 简化版本
            axes[1, 1].plot(epochs[:len(lr_history)], lr_history, label='Learning Rate', color='red')
            axes[1, 1].set_title('Learning Rate')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('LR')
            axes[1, 1].legend()
            axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()


def create_data_loaders(data_config: Dict[str, Any], model_config: ModelConfig, 
                       batch_size: int = 32, val_split: float = 0.2) -> Tuple[DataLoader, DataLoader]:
    """创建数据加载器"""
    
    # 创建DataHandler
    handler = DataHandler(
        data_loader=data_config,
        win=data_config.get('win', 20),
        step=data_config.get('step', 1),
        verbose=data_config.get('verbose', False),
        is_normal=data_config.get('is_normal', True),
        keep_period_separate=data_config.get('keep_period_separate', True),
        sel_fd_names=data_config.get('sel_fd_names', None)
    )
    
    # 创建FTSDataset
    fts_dataset = FTSDataset(
        handler=handler,
        model_type=0,  # 使用传统模式
        seq_len=data_config.get('seq_len', 20)
    )
    
    # 设置和准备数据
    fts_dataset.setup_data()
    fts_dataset.prepare(direct='ls', win=data_config.get('win', 20), filter_win=0)
    
    # 创建多周期数据集适配器
    dataset = MultiPeriodDataset(fts_dataset, model_config.period_configs)
    
    # 分割数据集
    total_size = len(dataset)
    val_size = int(total_size * val_split)
    train_size = total_size - val_size
    
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        num_workers=0,  # Windows下设为0
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    val_loader = DataLoader(
        val_dataset, 
        batch_size=batch_size, 
        shuffle=False, 
        num_workers=0,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    return train_loader, val_loader


def main():
    """主训练函数"""
    # 设置设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 数据配置
    data_config = {
        "data_path": "f:/featdata/tmp",
        "years": ["2025"],
        "fd_set": {(1, 0), (1, 1), (2, 0)},  # 多周期数据
        "win": 20,
        "step": 1,
        "seq_len": 20,
        "verbose": False,
        "is_normal": False,
        "keep_period_separate": True
    }
    
    # 获取预设模型配置
    preset_configs = get_preset_configs()
    
    # 选择要训练的模型
    model_name = 'lightweight_cnn'  # 可以改为其他模型
    model_config = preset_configs[model_name]
    
    print(f"训练模型: {model_name}")
    print(f"模型配置: {model_config}")
    
    # 训练配置
    train_config = {
        'learning_rate': 1e-3,
        'weight_decay': 1e-4,
        'epochs': 100,
        'patience': 20,
        'scheduler': 'cosine',
        'task_type': model_config.task_type
    }
    
    try:
        # 创建数据加载器
        print("创建数据加载器...")
        train_loader, val_loader = create_data_loaders(
            data_config, model_config, batch_size=32, val_split=0.2
        )
        
        print(f"训练集大小: {len(train_loader.dataset)}")
        print(f"验证集大小: {len(val_loader.dataset)}")
        
        # 创建模型
        print("创建模型...")
        model = ModelFactory.create_model(model_config)
        
        # 创建训练器
        trainer = ModelTrainer(model, train_config, device)
        
        # 开始训练
        save_path = f"best_model_{model_name}.pth"
        history = trainer.train(
            train_loader, val_loader, 
            epochs=train_config['epochs'], 
            save_path=save_path
        )
        
        # 绘制训练历史
        trainer.plot_training_history(f"training_history_{model_name}.png")
        
        print(f"训练完成！最佳模型保存至: {save_path}")
        
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def train_multiple_models():
    """训练多个模型进行对比"""
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    # 数据配置
    data_config = {
        "data_path": "f:/featdata/tmp",
        "years": ["2025"],
        "fd_set": {(1, 0), (1, 1), (2, 0)},
        "win": 20,
        "step": 1,
        "seq_len": 20,
        "verbose": False,
        "is_normal": False,
        "keep_period_separate": True
    }

    # 获取预设配置
    preset_configs = get_preset_configs()

    # 选择要对比的模型
    models_to_train = ['lightweight_cnn', 'standard_transformer', 'advanced_graph']

    results = {}

    for model_name in models_to_train:
        print(f"\n{'='*50}")
        print(f"训练模型: {model_name}")
        print(f"{'='*50}")

        try:
            model_config = preset_configs[model_name]

            # 训练配置
            train_config = {
                'learning_rate': 1e-3,
                'weight_decay': 1e-4,
                'epochs': 50,  # 快速对比，减少epoch
                'patience': 15,
                'scheduler': 'cosine',
                'task_type': model_config.task_type
            }

            # 创建数据加载器
            train_loader, val_loader = create_data_loaders(
                data_config, model_config, batch_size=32, val_split=0.2
            )

            # 创建模型和训练器
            model = ModelFactory.create_model(model_config)
            trainer = ModelTrainer(model, train_config, device)

            # 训练
            start_time = time.time()
            history = trainer.train(
                train_loader, val_loader,
                epochs=train_config['epochs'],
                save_path=f"best_model_{model_name}.pth"
            )
            training_time = time.time() - start_time

            # 记录结果
            best_val_loss = min(history['val_loss'])
            final_metrics = history['val_metrics'][-1]

            results[model_name] = {
                'best_val_loss': best_val_loss,
                'final_rmse': final_metrics.get('rmse', 0),
                'final_correlation': final_metrics.get('correlation', 0),
                'training_time': training_time,
                'model_params': sum(p.numel() for p in model.parameters()),
                'history': history
            }

            print(f"模型 {model_name} 训练完成:")
            print(f"  最佳验证损失: {best_val_loss:.4f}")
            print(f"  最终RMSE: {final_metrics.get('rmse', 0):.4f}")
            print(f"  最终相关系数: {final_metrics.get('correlation', 0):.4f}")
            print(f"  训练时间: {training_time:.1f}秒")

        except Exception as e:
            print(f"模型 {model_name} 训练失败: {e}")
            results[model_name] = {'error': str(e)}

    # 生成对比报告
    print(f"\n{'='*60}")
    print("模型对比结果")
    print(f"{'='*60}")

    comparison_df = pd.DataFrame([
        {
            'Model': name,
            'Best_Val_Loss': result.get('best_val_loss', 'N/A'),
            'Final_RMSE': result.get('final_rmse', 'N/A'),
            'Final_Correlation': result.get('final_correlation', 'N/A'),
            'Training_Time(s)': result.get('training_time', 'N/A'),
            'Model_Params': result.get('model_params', 'N/A')
        }
        for name, result in results.items()
        if 'error' not in result
    ])

    print(comparison_df.to_string(index=False))

    # 保存结果
    comparison_df.to_csv('model_comparison_results.csv', index=False)

    # 绘制对比图
    plot_model_comparison(results)

    return results


def plot_model_comparison(results: Dict[str, Dict]):
    """绘制模型对比图"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # 提取有效结果
    valid_results = {k: v for k, v in results.items() if 'error' not in v}

    if not valid_results:
        print("没有有效的训练结果可以绘制")
        return

    # 1. 验证损失对比
    for name, result in valid_results.items():
        history = result['history']
        epochs = range(len(history['val_loss']))
        axes[0, 0].plot(epochs, history['val_loss'], label=name)

    axes[0, 0].set_title('Validation Loss Comparison')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True)

    # 2. RMSE对比
    models = list(valid_results.keys())
    rmse_values = [result['final_rmse'] for result in valid_results.values()]

    axes[0, 1].bar(models, rmse_values, color=['blue', 'orange', 'green', 'red'][:len(models)])
    axes[0, 1].set_title('Final RMSE Comparison')
    axes[0, 1].set_ylabel('RMSE')
    axes[0, 1].tick_params(axis='x', rotation=45)

    # 3. 相关系数对比
    corr_values = [result['final_correlation'] for result in valid_results.values()]
    axes[1, 0].bar(models, corr_values, color=['blue', 'orange', 'green', 'red'][:len(models)])
    axes[1, 0].set_title('Final Correlation Comparison')
    axes[1, 0].set_ylabel('Correlation')
    axes[1, 0].tick_params(axis='x', rotation=45)

    # 4. 训练时间 vs 性能
    training_times = [result['training_time'] for result in valid_results.values()]
    axes[1, 1].scatter(training_times, rmse_values, s=100)

    for i, name in enumerate(models):
        axes[1, 1].annotate(name, (training_times[i], rmse_values[i]),
                           xytext=(5, 5), textcoords='offset points')

    axes[1, 1].set_title('Training Time vs Performance')
    axes[1, 1].set_xlabel('Training Time (s)')
    axes[1, 1].set_ylabel('RMSE')
    axes[1, 1].grid(True)

    plt.tight_layout()
    plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


def hyperparameter_tuning(model_name: str = 'lightweight_cnn'):
    """超参数调优"""
    device = 'cuda' if torch.cuda.is_available() else 'cpu'

    # 数据配置
    data_config = {
        "data_path": "f:/featdata/tmp",
        "years": ["2025"],
        "fd_set": {(1, 0), (1, 1), (2, 0)},
        "win": 20,
        "step": 1,
        "seq_len": 20,
        "verbose": False,
        "is_normal": False,
        "keep_period_separate": True
    }

    # 超参数搜索空间
    hyperparams = {
        'learning_rate': [1e-4, 3e-4, 1e-3, 3e-3],
        'weight_decay': [1e-5, 1e-4, 1e-3],
        'batch_size': [16, 32, 64]
    }

    best_score = float('inf')
    best_params = {}
    results = []

    print(f"开始超参数调优，模型: {model_name}")

    # 网格搜索
    for lr in hyperparams['learning_rate']:
        for wd in hyperparams['weight_decay']:
            for bs in hyperparams['batch_size']:
                print(f"\n测试参数: lr={lr}, wd={wd}, bs={bs}")

                try:
                    # 获取模型配置
                    preset_configs = get_preset_configs()
                    model_config = preset_configs[model_name]

                    # 训练配置
                    train_config = {
                        'learning_rate': lr,
                        'weight_decay': wd,
                        'epochs': 30,  # 快速调优
                        'patience': 10,
                        'scheduler': 'cosine',
                        'task_type': model_config.task_type
                    }

                    # 创建数据加载器
                    train_loader, val_loader = create_data_loaders(
                        data_config, model_config, batch_size=bs, val_split=0.2
                    )

                    # 创建模型和训练器
                    model = ModelFactory.create_model(model_config)
                    trainer = ModelTrainer(model, train_config, device)

                    # 训练
                    history = trainer.train(train_loader, val_loader, epochs=30)

                    # 评估结果
                    best_val_loss = min(history['val_loss'])
                    final_metrics = history['val_metrics'][-1]

                    result = {
                        'lr': lr,
                        'wd': wd,
                        'bs': bs,
                        'val_loss': best_val_loss,
                        'rmse': final_metrics.get('rmse', 0),
                        'correlation': final_metrics.get('correlation', 0)
                    }

                    results.append(result)

                    print(f"  验证损失: {best_val_loss:.4f}")
                    print(f"  RMSE: {final_metrics.get('rmse', 0):.4f}")

                    # 更新最佳参数
                    if best_val_loss < best_score:
                        best_score = best_val_loss
                        best_params = {'lr': lr, 'wd': wd, 'bs': bs}
                        print(f"  *** 新的最佳参数! ***")

                except Exception as e:
                    print(f"  参数组合失败: {e}")

    # 保存调优结果
    results_df = pd.DataFrame(results)
    results_df.to_csv(f'hyperparameter_tuning_{model_name}.csv', index=False)

    print(f"\n超参数调优完成!")
    print(f"最佳参数: {best_params}")
    print(f"最佳验证损失: {best_score:.4f}")

    return best_params, results


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='训练新模型架构')
    parser.add_argument('--mode', type=str, default='single',
                       choices=['single', 'compare', 'tune'],
                       help='运行模式: single(单模型), compare(多模型对比), tune(超参数调优)')
    parser.add_argument('--model', type=str, default='lightweight_cnn',
                       help='模型名称')

    args = parser.parse_args()

    if args.mode == 'single':
        main()
    elif args.mode == 'compare':
        train_multiple_models()
    elif args.mode == 'tune':
        hyperparameter_tuning(args.model)
