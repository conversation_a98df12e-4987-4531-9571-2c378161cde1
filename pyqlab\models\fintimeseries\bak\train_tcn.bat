e:
cd e:\lab\RoboQuant\pylab

@REM 10HF ===========================
@REM python ./pyqlab/pl/train_tcn.py --kernel_size=3 --batch_size=512 --k_folds=4 --max_epochs=8 --out_channels="(100, 100, 100, 100)" --model_name=temporal_conv_net --ins_nums="(0, 51, 0, 0)" --dropout=0.1

@REM python ./pyqlab/pl/train_tcn.py --kernel_size=2 --batch_size=512 --k_folds=4 --max_epochs=8 --out_channels="(200, 200, 200, 200, 200)" --model_name=temporal_conv_net --ins_nums="(0, 51, 51, 0)" --dropout=0.1


@REM 备选
python ./pyqlab/pl/train_tcn.py --kernel_size=2 --start_time=2023-06-01 --end_time=2023-12-31 --batch_size=512 --k_folds=5 --max_epochs=7 --out_channels="(200, 400, 600)" --model_name=temporal_conv_net --ins_nums="(0, 51, 51, 0)" --dropout=0.1

@REM python ./pyqlab/pl/train_tcn.py --kernel_size=2 --ds_name=10HF --num_channels=10 --batch_size=480 --k_folds=4 --max_epochs=8 --out_channels="(400, 400, 400, 400)" --model_name=temporal_conv_net --ins_nums="(0, 51, 51, 0)" --dropout=0.1

@REM 16HF ===========================
@REM python ./pyqlab/pl/train_tcn.py --kernel_size=2 --ds_name=16HF --num_channels=16 --batch_size=256 --k_folds=5 --max_epochs=7 --out_channels="(400, 400, 400, 400, 400)" --model_name=temporal_conv_net --ins_nums="(0, 51, 51, 0)" --dropout=0.1

@REM python ./pyqlab/pl/train_tcn.py --kernel_size=2 --ds_name=15HF --num_channels=15 --batch_size=512 --k_folds=4 --max_epochs=8 --out_channels="(200, 200, 200, 200)" --model_name=temporal_conv_net --ins_nums="(0, 51, 51, 0)" --dropout=0.1

@REM python ./pyqlab/pl/train_tcn.py --batch_size=512 --k_folds=4 --max_epochs=7 --out_channels="(200, 200, 200)" --model_name=temporal_conv_net --dropout=0.1

@REM python ./pyqlab/pl/train_tcn.py --ds_files=['main.2022'] --batch_size=512 --k_folds=4 --max_epochs=7 --activation=relu --out_channels="(24, 48, 1200, 1200)" --model_name=tcn_model --dropout=0.5

@REM AG ===========================
@REM python ./pyqlab/pl/train_tcn.py --version="TCN-AG" --data_path="e:/featdata/ag" --ds_files="['ag.2015','ag.2016','ag.2017','ag.2018','ag.2019','ag.2020','ag.2021','ag.2022','ag.2023']" --kernel_size=2 --batch_size=512 --k_folds=4 --max_epochs=8 --out_channels="(200, 400, 600)" --model_name=temporal_conv_net --num_embeds="[0]" --ins_nums="(0, 51, 51, 2)" --dropout=0.1

@REM SF ===========================
@REM python ./pyqlab/pl/train_tcn.py --version="TCN-SF" --fut_codes=SF_FUT_CODES --data_path="e:/featdata/sf" --ds_files="['sf.2018','sf.2019','sf.2020','sf.2021','sf.2022','sf.2023']" --kernel_size=2 --batch_size=512 --k_folds=4 --max_epochs=8 --out_channels="(200, 400, 600)" --model_name=temporal_conv_net --ins_nums="(0, 51, 51, 0)" --dropout=0.0


PAUSE